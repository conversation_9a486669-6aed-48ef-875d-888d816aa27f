import { createRouter, createWebHistory } from 'vue-router'
import HomeView from '../views/HomeView.vue'
import { useAuthStore } from '@/stores/auth'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'home',
      component: HomeView,
    },
    {
      path: '/about',
      name: 'about',
      component: () => import('../views/AboutView.vue'),
    },

    // Patent Routes
    {
      path: '/patents',
      children: [
        {
          path: 'upload',
          name: 'patent-upload',
          component: () => import('../views/patents/PatentUploadView.vue'),
          meta: { requiresAuth: true, roles: ['user'] }
        },
        {
          path: 'search',
          name: 'patent-search',
          component: () => import('../views/patents/PatentSearchView.vue'),
          meta: { requiresAuth: true }
        },
        {
          path: 'my-patents',
          name: 'my-patents',
          component: () => import('../views/patents/MyPatentsView.vue'),
          meta: { requiresAuth: true, roles: ['user'] }
        },
        {
          path: 'trading',
          name: 'patent-trading',
          component: () => import('../views/patents/PatentTradingView.vue'),
          meta: { requiresAuth: true, roles: ['user'] }
        },
        {
          path: 'protection',
          name: 'patent-protection',
          component: () => import('../views/patents/PatentProtectionView.vue'),
          meta: { requiresAuth: true, roles: ['user'] }
        }
      ]
    },

    // Review Routes
    {
      path: '/review',
      children: [
        {
          path: 'pending',
          name: 'review-pending',
          component: () => import('../views/review/ReviewPendingView.vue'),
          meta: { requiresAuth: true, roles: ['reviewer'] }
        },
        {
          path: 'upload',
          name: 'review-upload',
          component: () => import('../views/review/ReviewUploadView.vue'),
          meta: { requiresAuth: true, roles: ['reviewer'] }
        },
        {
          path: 'trading',
          name: 'review-trading',
          component: () => import('../views/review/ReviewTradingView.vue'),
          meta: { requiresAuth: true, roles: ['reviewer'] }
        },
        {
          path: 'protection',
          name: 'review-protection',
          component: () => import('../views/review/ReviewProtectionView.vue'),
          meta: { requiresAuth: true, roles: ['reviewer'] }
        }
      ]
    },

    // Admin Routes
    {
      path: '/admin',
      children: [
        {
          path: 'users',
          name: 'admin-users',
          component: () => import('../views/admin/AdminUsersView.vue'),
          meta: { requiresAuth: true, roles: ['admin'] }
        },
        {
          path: 'transactions',
          name: 'admin-transactions',
          component: () => import('../views/admin/AdminTransactionsView.vue'),
          meta: { requiresAuth: true, roles: ['admin'] }
        },
        {
          path: 'statistics',
          name: 'admin-statistics',
          component: () => import('../views/admin/AdminStatisticsView.vue'),
          meta: { requiresAuth: true, roles: ['admin'] }
        }
      ]
    }
  ],
})

// Navigation guard
router.beforeEach((to, from, next) => {
  const authStore = useAuthStore()

  // Check if route requires authentication
  if (to.meta.requiresAuth) {
    if (!authStore.isConnected) {
      // Redirect to home if not connected
      next('/')
      return
    }

    // Check role-based access
    if (to.meta.roles && !to.meta.roles.includes(authStore.userRole)) {
      // Redirect to home if user doesn't have required role
      next('/')
      return
    }
  }

  next()
})

export default router
