import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export const useAuthStore = defineStore('auth', () => {
  // State
  const account = ref(null)
  const isConnected = ref(false)
  const userRole = ref(null) // 'user', 'reviewer', 'admin'
  const web3 = ref(null)
  const isLoading = ref(false)
  const error = ref(null)

  // Getters
  const isUser = computed(() => userRole.value === 'user')
  const isReviewer = computed(() => userRole.value === 'reviewer')
  const isAdmin = computed(() => userRole.value === 'admin')
  const shortAddress = computed(() => {
    if (!account.value) return ''
    return `${account.value.slice(0, 6)}...${account.value.slice(-4)}`
  })

  // Actions
  const connectWallet = async () => {
    try {
      isLoading.value = true
      error.value = null

      // Check if MetaMask is installed
      if (typeof window.ethereum === 'undefined') {
        throw new Error('请安装 MetaMask 钱包')
      }

      // Request account access
      const accounts = await window.ethereum.request({
        method: 'eth_requestAccounts'
      })

      if (accounts.length === 0) {
        throw new Error('未找到钱包账户')
      }

      account.value = accounts[0]
      isConnected.value = true

      // Initialize Web3
      const Web3 = (await import('web3')).default
      web3.value = new Web3(window.ethereum)

      // Determine user role (this would typically come from smart contract)
      await determineUserRole()

      // Listen for account changes
      window.ethereum.on('accountsChanged', handleAccountsChanged)
      window.ethereum.on('chainChanged', handleChainChanged)

    } catch (err) {
      error.value = err.message
      console.error('钱包连接失败:', err)
    } finally {
      isLoading.value = false
    }
  }

  const disconnectWallet = () => {
    account.value = null
    isConnected.value = false
    userRole.value = null
    web3.value = null
    error.value = null

    // Remove event listeners
    if (window.ethereum) {
      window.ethereum.removeListener('accountsChanged', handleAccountsChanged)
      window.ethereum.removeListener('chainChanged', handleChainChanged)
    }
  }

  const determineUserRole = async () => {
    // This is a simplified role determination
    // In a real application, this would query the smart contract
    // For demo purposes, we'll assign roles based on address patterns
    if (!account.value) return

    const address = account.value.toLowerCase()
    
    // Admin addresses (you can modify these)
    const adminAddresses = ['0x...admin1', '0x...admin2']
    
    // Reviewer addresses (you can modify these)
    const reviewerAddresses = ['0x...reviewer1', '0x...reviewer2']

    if (adminAddresses.includes(address)) {
      userRole.value = 'admin'
    } else if (reviewerAddresses.includes(address)) {
      userRole.value = 'reviewer'
    } else {
      userRole.value = 'user'
    }
  }

  const handleAccountsChanged = (accounts) => {
    if (accounts.length === 0) {
      disconnectWallet()
    } else {
      account.value = accounts[0]
      determineUserRole()
    }
  }

  const handleChainChanged = () => {
    // Reload the page when chain changes
    window.location.reload()
  }

  const checkConnection = async () => {
    try {
      if (typeof window.ethereum !== 'undefined') {
        const accounts = await window.ethereum.request({
          method: 'eth_accounts'
        })
        
        if (accounts.length > 0) {
          account.value = accounts[0]
          isConnected.value = true
          
          const Web3 = (await import('web3')).default
          web3.value = new Web3(window.ethereum)
          
          await determineUserRole()
          
          // Set up event listeners
          window.ethereum.on('accountsChanged', handleAccountsChanged)
          window.ethereum.on('chainChanged', handleChainChanged)
        }
      }
    } catch (err) {
      console.error('检查连接状态失败:', err)
    }
  }

  return {
    // State
    account,
    isConnected,
    userRole,
    web3,
    isLoading,
    error,
    
    // Getters
    isUser,
    isReviewer,
    isAdmin,
    shortAddress,
    
    // Actions
    connectWallet,
    disconnectWallet,
    checkConnection
  }
})
